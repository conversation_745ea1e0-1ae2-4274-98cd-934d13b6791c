buildscript {
    repositories {
        maven {
            name = 'GTNH Maven'
            url = 'https://nexus.gtnewhorizons.com/repository/public/'
        }
        maven {
            name = 'forge'
            url = 'https://maven.minecraftforge.net'
        }
        maven {
            name = 'sonatype'
            url = 'https://oss.sonatype.org/content/repositories/snapshots/'
        }
        gradlePluginPortal()
        mavenCentral()
    }
    dependencies {
        classpath 'com.gtnewhorizons:retrofuturagradle:1.3.+'
        classpath 'com.guardsquare:proguard-gradle:7.4.0'
    }
}

apply plugin: 'com.gtnewhorizons.retrofuturagradle'
apply plugin: 'maven-publish'
apply plugin: 'com.guardsquare.proguard'

version = project.mod_version
group = project.mod_group
archivesBaseName = project.mod_id

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(8)
    }
}

minecraft {
    mcVersion = '1.7.10'
    username = 'Developer'
}

repositories {
    maven {
        name = 'GTNH Maven'
        url = 'https://nexus.gtnewhorizons.com/repository/public/'
    }
    maven {
        name = 'forge'
        url = 'https://maven.minecraftforge.net'
    }
    mavenCentral()
}

dependencies {
    // 不使用外部依赖，避免签名冲突
    // 使用纯 Java 文件存储系统
}

processResources {
    inputs.property "version", project.version
    inputs.property "mcversion", project.minecraft.version

    duplicatesStrategy = DuplicatesStrategy.INCLUDE

    from(sourceSets.main.resources.srcDirs) {
        include 'mcmod.info'
        expand 'version': project.version, 'mcversion': project.minecraft.version
    }

    from(sourceSets.main.resources.srcDirs) {
        exclude 'mcmod.info'
    }
}

// 标准 JAR 配置 - 不包含任何外部依赖
jar {
    // 只包含我们自己的类，不包含任何依赖
}

task sourcesJar(type: Jar) {
    from sourceSets.main.allSource
    archiveClassifier = 'sources'
}

task javadocJar(type: Jar) {
    from javadoc
    archiveClassifier = 'javadoc'
}

artifacts {
    archives sourcesJar
    archives javadocJar
}

publishing {
    publications {
        maven(MavenPublication) {
            from components.java

            artifact sourcesJar
            artifact javadocJar
        }
    }
}

// 混淆任务
task obfuscateJar(type: Jar) {
    dependsOn jar
    archiveClassifier = 'obfuscated'
    from zipTree(jar.archiveFile)

    // 简单的类名混淆处理
    eachFile { file ->
        if (file.path.endsWith('.class')) {
            // 混淆内部类名（保留公共接口）
            if (!file.path.contains('CrossPlatformBanMod') &&
                !file.path.contains('ClientProxy') &&
                !file.path.contains('ServerProxy') &&
                !file.path.contains('CommonProxy') &&
                !file.path.contains('BanCommand') &&
                !file.path.contains('network/')) {
                // 重命名内部实现类
                def newName = file.path.replaceAll(/([A-Z][a-z]+)/, { match ->
                    return 'Obf' + match[1].charAt(0) + (Math.abs(match[1].hashCode()) % 1000)
                })
                file.path = newName
            }
        }
    }
}

// ProGuard 混淆配置
task obfuscate(type: proguard.gradle.ProGuardTask) {
    dependsOn jar

    // 输入和输出
    injars jar.archiveFile
    outjars "${buildDir}/libs/${archivesBaseName}-${version}-obfuscated.jar"

    // 库文件
    libraryjars "${System.getProperty('java.home')}/lib/rt.jar"
    libraryjars configurations.runtimeClasspath

    // 保留主要类
    keep 'public class com.anticheat.crossplatformban.CrossPlatformBanMod { *; }'
    keep 'public class com.anticheat.crossplatformban.client.ClientProxy { *; }'
    keep 'public class com.anticheat.crossplatformban.server.ServerProxy { *; }'
    keep 'public class com.anticheat.crossplatformban.common.CommonProxy { *; }'
    keep 'class * { @cpw.mods.fml.common.Mod$EventHandler *; }'
    keep 'class * { @cpw.mods.fml.common.eventhandler.SubscribeEvent *; }'
    keep 'class com.anticheat.crossplatformban.common.network.** { *; }'
    keep 'class com.anticheat.crossplatformban.server.BanCommand { *; }'

    // 混淆选项
    dontoptimize
    dontpreverify
    keepattributes 'LineNumberTable,SourceFile'

    printmapping "${buildDir}/proguard/mapping.txt"
}

// ProGuard 混淆配置
task obfuscate(type: proguard.gradle.ProGuardTask) {
    dependsOn jar

    // 输入和输出
    injars jar.archiveFile
    outjars "${buildDir}/libs/${archivesBaseName}-${version}-obfuscated.jar"

    // 库文件
    libraryjars "${System.getProperty('java.home')}/lib/rt.jar"
    libraryjars configurations.runtimeClasspath

    // 保留主要类
    keep 'public class com.anticheat.crossplatformban.CrossPlatformBanMod { *; }'
    keep 'public class com.anticheat.crossplatformban.client.ClientProxy { *; }'
    keep 'public class com.anticheat.crossplatformban.server.ServerProxy { *; }'
    keep 'public class com.anticheat.crossplatformban.common.CommonProxy { *; }'
    keep 'class * { @cpw.mods.fml.common.Mod$EventHandler *; }'
    keep 'class * { @cpw.mods.fml.common.eventhandler.SubscribeEvent *; }'
    keep 'class com.anticheat.crossplatformban.common.network.** { *; }'
    keep 'class com.anticheat.crossplatformban.server.BanCommand { *; }'

    // 混淆选项
    dontoptimize
    dontpreverify
    keepattributes 'LineNumberTable,SourceFile'

    printmapping "${buildDir}/proguard/mapping.txt"
}

// ProGuard 混淆配置
task obfuscate(type: proguard.gradle.ProGuardTask) {
    dependsOn jar

    // 输入 JAR
    injars jar.archiveFile

    // 输出混淆后的 JAR
    outjars "${buildDir}/libs/${archivesBaseName}-${version}-obfuscated.jar"

    // 保留 Minecraft Forge 和 FML 相关类
    libraryjars "${System.getProperty('java.home')}/lib/rt.jar"
    libraryjars configurations.runtimeClasspath

    // 保留主要入口点
    keep 'public class com.anticheat.crossplatformban.CrossPlatformBanMod { *; }'
    keep 'public class com.anticheat.crossplatformban.client.ClientProxy { *; }'
    keep 'public class com.anticheat.crossplatformban.server.ServerProxy { *; }'
    keep 'public class com.anticheat.crossplatformban.common.CommonProxy { *; }'

    // 保留 Forge 注解的方法
    keep 'class * { @cpw.mods.fml.common.Mod$EventHandler *; }'
    keep 'class * { @cpw.mods.fml.common.event.* *; }'
    keep 'class * { @cpw.mods.fml.common.eventhandler.SubscribeEvent *; }'
    keep 'class * { @net.minecraftforge.event.* *; }'

    // 保留网络包相关类（需要序列化）
    keep 'class com.anticheat.crossplatformban.common.network.** { *; }'

    // 保留命令类
    keep 'class com.anticheat.crossplatformban.server.BanCommand { *; }'

    // 混淆选项
    dontoptimize  // 不优化，避免破坏 Minecraft 兼容性
    dontpreverify // 不预验证

    // 保留行号信息（用于调试）
    keepattributes 'LineNumberTable,SourceFile'
}

// ProGuard 混淆配置
task obfuscate(type: proguard.gradle.ProGuardTask) {
    dependsOn jar

    // 输入 JAR
    injars jar.archiveFile

    // 输出混淆后的 JAR
    outjars "${buildDir}/libs/${archivesBaseName}-${version}-obfuscated.jar"

    // 保留 Minecraft Forge 和 FML 相关类
    libraryjars "${System.getProperty('java.home')}/lib/rt.jar"
    libraryjars configurations.runtimeClasspath

    // 保留主要入口点
    keep 'public class com.anticheat.crossplatformban.CrossPlatformBanMod { *; }'
    keep 'public class com.anticheat.crossplatformban.client.ClientProxy { *; }'
    keep 'public class com.anticheat.crossplatformban.server.ServerProxy { *; }'
    keep 'public class com.anticheat.crossplatformban.common.CommonProxy { *; }'

    // 保留 Forge 注解的方法
    keep 'class * { @cpw.mods.fml.common.Mod$EventHandler *; }'
    keep 'class * { @cpw.mods.fml.common.event.* *; }'
    keep 'class * { @cpw.mods.fml.common.eventhandler.SubscribeEvent *; }'
    keep 'class * { @net.minecraftforge.event.* *; }'

    // 保留网络包相关类（需要序列化）
    keep 'class com.anticheat.crossplatformban.common.network.** { *; }'

    // 保留命令类
    keep 'class com.anticheat.crossplatformban.server.BanCommand { *; }'

    // 混淆选项
    dontoptimize  // 不优化，避免破坏 Minecraft 兼容性
    dontpreverify // 不预验证

    // 保留行号信息（用于调试）
    keepattributes 'LineNumberTable,SourceFile'

    // 混淆类名和方法名，但保留必要的
    obfuscationdictionary 'obfuscation-dictionary.txt'
    classobfuscationdictionary 'obfuscation-dictionary.txt'
    packageobfuscationdictionary 'obfuscation-dictionary.txt'

    // 重命名源文件
    renamesourcefileattribute 'SourceFile'

    // 混淆核心反作弊逻辑，但保留公共接口
    keep 'public class com.anticheat.crossplatformban.common.database.BanDatabase {' +
         '    public *;' +
         '}'

    keep 'public class com.anticheat.crossplatformban.server.MachineCodeVerifier {' +
         '    public static void handleMachineCodeResponse(...);' +
         '}'

    // 保留客户端机器码收集器的公共方法
    keep 'public class com.anticheat.crossplatformban.client.MachineCodeCollector {' +
         '    public static java.lang.String collectMachineCode();' +
         '}'

    printmapping "${buildDir}/proguard/mapping.txt"
    printseeds "${buildDir}/proguard/seeds.txt"
    printusage "${buildDir}/proguard/usage.txt"
}
